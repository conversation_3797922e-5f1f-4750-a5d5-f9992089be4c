#!/bin/bash

# https://dev.outlook.com/Connectors/GetStarted#posting-more-complex-cards
# https://messagecardplayground.azurewebsites.net/
# based on work: https://github.com/ericoc/zabbix-slack-alertscript

# Variables
curlheader='-H "Content-Type: application/json"'
# Teams incoming web-hook URL and user name
#url='https://outlook.office.com/webhook/01ac4511-369b-44a4-af98-e215c401b3d2@b59a8795-364b-44c6-92ff-95342a294254/IncomingWebhook/5240f0361bf340028e47646b19878a02/f3520f47-4775-43b6-891d-7ce4f03e2972'
url='https://szkuat.webhook.office.com/webhookb2/01ac4511-369b-44a4-af98-e215c401b3d2@b59a8795-364b-44c6-92ff-95342a294254/IncomingWebhook/5240f0361bf340028e47646b19878a02/f3520f47-4775-43b6-891d-7ce4f03e2972'

#url="https://outlook.office.com/webhook/01ac4511-369b-44a4-af98-e215c401b3d2@b59a8795-364b-44c6-92ff-95342a294254/IncomingWebhook/1e80d030a3514729a013dd575c6b2318/f3520f47-4775-43b6-891d-7ce4f03e2972"
agent='-A "zabbix-teams-alertscript / https://github.com/ericoc/zabbix-slack-alertscript"'
curlmaxtime='-m 60'
date=$(date)
extracturl=""
title=""
image="https://thumbs.dreamstime.com/z/funny-man-watermelon-helmet-googles-looks-like-parasitic-caterpillar-34231133.jpg"
#heroimage="https://vignette.wikia.nocookie.net/southpark/images/c/c2/Craig-tucker.png"

# Get params from script
subject="$2"
message="${subject}: $3"
triggerurl="$4"

# seto options
shopt -s nocasematch

# Change message themeColor depending on the subject - green (RECOVERY), red (PROBLEM), or grey (for everything else)
recoversub="^(resolved|recovery|recovered).*"
problemsub="^problem.*"
oksub="^OK"
freespacesub='free\s{0,3}disk\s{0,3}space(\W|$)'
rebootsub='has\s{0,3}just\s{0,3}been\s{0,3}restarted(\W|$)'
cpusub='load\s{0,3}is\s{0,3}too\s{0,3}high(\W|$)'
unavailablesub='is\s{0,3}unreachable\s{0,3}for(\W|$)'
loginsub='problem\s{0,3}login(\W|$)'
sqljobsub='windows\s{0,3}failed(\W|$)'
memsosub='Lack\s{0,3}of\s{0,3}available\s{0,3}memory(\W|$)'
healthsub='DistributedApplicationHealthcheck(\W|$)'
apppoolsub='AppPool\s{0,3}is\s{0,3}down(\W|$)'
if [[ "$subject" =~ ${recoversub} ]]; then
        THEMECOLOR='43EA00'
        title="Scotty fixed a problem. Good job Scotty"
        image="https://i.ibb.co/ZdrCcpj/Selection-999-704.png"

elif [[ "$subject" =~ ${oksub} ]]; then
        THEMECOLOR='43EA00'
        title="Scotty fixed a problem. Good job Scotty"
        image="https://memegenerator.net/img/instances/83995837.jpg"

elif [[ "$subject" =~ ${freespacesub} ]]; then
        THEMECOLOR='EA0000'
        title="Scotty not cleaning your shit. Do the needful!"
        image="https://i.pinimg.com/originals/46/da/11/46da11ccac69fbc40da5ce204f30e5e8.jpg"

elif [[ "$subject" =~ ${cpusub} ]]; then
        THEMECOLOR='EA0000'
        title="Scotty asks: are you HIGH?. Because your server CPUs are"
        image="https://pics.conservativememes.com/are-youhighed-memess-com-14989380.png"

elif [[ "$subject" =~ ${rebootsub} ]]; then
        THEMECOLOR='E23000'
        title="Scotty rebooted your important server. Are you happy now?"
        image="https://i.chzbgr.com/full/6684870656/hF95CF2CD/okay-ill-say-it-ouch-are-you-happy-now"

elif [[ "$subject" =~ ${unavailablesub} ]]; then
        THEMECOLOR='E23000'
        title="Scotty thinks your sever is missing. Come with me if you wanna live!"
        image="https://s-media-cache-ak0.pinimg.com/236x/56/29/d0/5629d0ffff6f36b4e2af7929ca3f16fb--terminator--south-park.jpg"

elif [[ "$subject" =~ ${loginsub} ]]; then
        THEMECOLOR='E23000'
        title="Scotty forbids you to access. Princess is in another castle"
        image="https://vignette1.wikia.nocookie.net/southpark/images/3/35/Princess_Kenny_.png"

elif [[ "$subject" =~ ${sqljobsub} ]]; then
        THEMECOLOR='E23000'
        title="Scotty noticed failed job. Hackathon failed demo? Don't drink and deploy!"
        image="http://www.thinkgeek.com/images/products/zoom/khhn_south_park_hankey_plush_ornament.jpg"

elif [[ "$subject" =~ ${problemsub} ]]; then
        THEMECOLOR='EA4300'
        title="Scotty found an problem. Scotty doesn't know. Maybe use the safe word?"
        image="https://img-comment-fun.9cache.com/media/aoZLR2n/aM72AMkg_700w_0.jpg"
elif [[ "$subject" =~ ${memsosub} ]]; then
        THEMECOLOR='EA4300'
        title="Scotty have an memory problem"
        image="http://www.quickmeme.com/img/d6/d695d51d4d415446484baa9646aadcea36ae908e00336551f5cec953399748bb.jpg"
elif [[ "$subject" =~ ${healthsub} ]]; then
        THEMECOLOR='EA4300'
        title="Scotty check your health and test is positive"
        image="https://3.bp.blogspot.com/-VQn40VK-mm0/XFBkxen50sI/AAAAAAAAAK8/qh048Kum6hIPIT2GgWu9kXi8_fubmekXwCLcBGAs/s1600/PA120417.jpg"
elif [[ "$subject" =~ ${apppoolsub} ]]; then
        THEMECOLOR='EA4300'
        title="Scotty found an problem in your pool"
        image="https://odditymall.com/includes/content/poop-emoji-pool-float-thumb.jpg"
else
        THEMECOLOR='FFFF00'
        title="Scotty found something. Beam Scotty up!"
        image="https://i.ibb.co/syqjyDc/1-5-P-E2-D8c-VGv0pxuf-UCf8g.jpg"
fi


# change URL if needed
extracturl=$(echo "$message"|grep -oP 'http.?://\S+')

if [ -z "$extracturl" ];
        then
        extracturl="https://zabbix.szkuat.pl/zabbix/zabbix.php?action=dashboard.view"

fi

#eval curl $curlmaxtime $curlheader $curldata $url $agent
eval curl "$curlmaxtime" "$curlheader" "$url" "$agent" \
--data @<(cat <<EOF
{
    "@type": "MessageCard",
    "@context": "http://schema.org/extensions",
    "originator": "zabbix.szkuat.pl",
    "summary": "\"Zabbix Board\"",
    "themeColor": "${THEMECOLOR}",
    "title": "${title}",
    "sections": [
        {
            "activityTitle": "${subject}",
            "activitySubtitle": "${date}",
            "activityImage": "${image}",
            "text": "${message}",
            "potentialAction": [
                {
                    "@type": "OpenUri",
                    "name": "View in Zabbix",
                    "targets": [
                        {
                            "os": "default",
                            "uri": "${triggerurl}"
                        }
                    ]
                }
            ]
        }
    ]
}
EOF
)
exit 0
