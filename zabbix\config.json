{"mysql": {"host": "mysql:3306", "user": "semaphore", "pass": "AffectPen4Send", "name": "semaphore", "options": null}, "bolt": {"host": "", "user": "", "pass": "", "name": "", "options": null}, "dialect": "mysql", "port": "", "interface": "", "tmp_path": "/tmp/semaphore", "cookie_hash": "gp3bXBnNII2l4Qzb1MrAnV8RVXX18s0k5nf3Gwa7LqU=", "cookie_encryption": "OhPH4yXXiecxbhBBZe8CAOyqypuwW/w6uwR1nsBTybk=", "access_key_encryption": "CHSFdshtFkAtXQCOettUvZXs2VogaHxENVX28HqwY1M=", "email_sender": "<EMAIL>", "email_host": "smtp.szkuat.pl", "email_port": "25", "email_username": "", "email_password": "", "web_host": "", "ldap_binddn": "CN=ldap_semaphore,CN=Users,DC=szkuat,DC=pl", "ldap_bindpassword": "3bB3PizXfSrsae6ZK", "ldap_server": "szkuat.onmicrosoft.com:636", "ldap_searchdn": "DC=szkuat,DC=pl", "ldap_searchfilter": "(&(objectClass=user)(sAMAccountName=%s)(memberOf=CN=semaphore,CN=Users,DC=szkuat,DC=pl))", "ldap_mappings": {"dn": "<PERSON><PERSON><PERSON>", "mail": "mail", "uid": "sAMAccountName", "cn": "cn"}, "telegram_chat": "", "telegram_token": "", "slack_url": "", "max_parallel_tasks": 0, "email_alert": true, "email_secure": false, "telegram_alert": false, "slack_alert": false, "ldap_enable": true, "ldap_needtls": false, "ssh_config_path": "", "demo_mode": false, "git_client": ""}