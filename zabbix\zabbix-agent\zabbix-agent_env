# ZBX_SOURCEIP=
ZBX_DEBUGLEVEL=3
# ZBX_ENABLEREMOTECOMMANDS=0
# ZBX_LOGREMOTECOMMANDS=0
# ZBX_HOSTINTERFACE= # Available since 4.4.0
# ZBX_HOSTINTERFACEITEM= # Available since 4.4.0
ZBX_SERVER_HOST=zabbix-server
# ZBX_PASSIVE_ALLOW=true
ZBX_PASSIVESERVERS=**********/16
ZBX_ACTIVE_ALLOW=true
# ZBX_ACTIVESERVERS=zabbix-server
# ZBX_LISTENIP=
# ZBX_STARTAGENTS=3
ZBX_HOSTNAME=zabbix-server
# ZBX_HOSTNAMEITEM=system.hostname
# ZBX_METADATA=
# ZBX_METADATAITEM=
# ZBX_REFRESHACTIVECHECKS=120
# ZBX_BUFFERSEND=5
# ZBX_BUFFERSIZE=100
# ZBX_MAXLINESPERSECOND=20
# ZBX_ALIAS=""
# ZBX_TIMEOUT=3
# ZBX_UNSAFEUSERPARAMETERS=0
ZBX_LOADMODULE="docker,kernel,agent,postgresql"
# ZBX_TLSCONNECT=unencrypted
# ZBX_TLSACCEPT=unencrypted
# ZBX_TLSCAFILE=
# ZBX_TLSCRLFILE=
# ZBX_TLSSERVERCERTISSUER=
# ZBX_TLSSERVERCERTSUBJECT=
# ZBX_TLSCERTFILE=
# ZBX_TLSKEYFILE=
# ZBX_TLSPSKIDENTITY=
# ZBX_TLSPSKFILE=
