# zabbix-compose

zabbix-compose

## Postgres dump/restore

```psql
drop database zabbix;
create database zabbix;
GRANT ALL ON DATABASE zabbix TO zabbix;
ALTER USER zabbix WITH PASSWORD 'zabbixpassword';
```

```bash
pg_dump -v -Fc -U zabbix --file=/backup/20201210.dump zabbix
```

```bash
pg_restore -Fvc -j 8  file.dump
```

```bash
docker-compose exec postgres-server pg_restore -Fc -v -j 4  /var/lib/postgresql/data/backup/20210324.tgz -d zabbix -U zabbix
```

```bash
docker-compose exec postgres-server pg_restore -Fc -v -j 8  /backup/file2.dump -d zabbix -U zabbix
```

```psql
SELECT *, pg_size_pretty(total_bytes) AS total     , pg_size_pretty(index_bytes) AS INDEX     , pg_size_pretty(toast_bytes) AS toast     , pg_size_pretty(table_bytes) AS TABLE   FROM (   SELECT *, total_bytes-index_bytes-COALESCE(toast_bytes,0) AS table_bytes FROM (       SELECT c.oid,nspname AS table_schema, relname AS TABLE_NAME               , c.reltuples AS row_estimate               , pg_total_relation_size(c.oid) AS total_bytes               , pg_indexes_size(c.oid) AS index_bytes               , pg_total_relation_size(reltoastrelid) AS toast_bytes           FROM pg_class c           LEFT JOIN pg_namespace n ON n.oid = c.relnamespace           WHERE relkind = 'r'   ) a ) a;
```

```bash
/home/<USER>/oec/opsgenie-zabbix/send2opsgenie -triggerName='zabbix test alert' -triggerStatus='PROBLEM'
```
