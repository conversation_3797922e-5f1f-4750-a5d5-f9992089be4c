#FROM itoacr.azurecr.io/ito/sp-certs:0.8 AS certs
FROM semaphoreui/semaphore:v2.12.4 AS builder
SHELL ["/bin/ash", "-euo", "pipefail", "-c"]
ENV TZ=Europe/Warsaw
ENV PIP_ROOT_USER_ACTION=ignore
USER root
RUN apk add --no-cache sshpass git curl mysql-client openssh-client-default tini py3-aiohttp ca-certificates bash python3 python3-dev musl-dev krb5 krb5-dev krb5-libs gcc ansible py3-pip rsync && \
    mkdir -p /tmp/semaphore && \
    mkdir -p /etc/semaphore && \
    mkdir -p /var/lib/semaphore && \
    chown -R semaphore:0 /tmp/semaphore && \
    chown -R semaphore:0 /etc/semaphore && \
    chown -R semaphore:0 /var/lib/semaphore && \
    mkdir -p ~/.ssh \
    && eval "$(ssh-agent -s)" \
    && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config \
    && chmod -R 700 ~/.ssh
RUN pip3 install ansible-core &&\
    pip3 install jmespath &&\
    pip3 install --upgrade pywinrm &&\
    pip3 install --upgrade pykerberos &&\
    pip3 install "urllib3 <=1.26.15" &&\
  ansible-galaxy collection install --upgrade ansible.posix &&\
  ansible-galaxy collection install --upgrade ansible.windows &&\
  ansible-galaxy collection install --upgrade chocolatey.chocolatey &&\
  ansible-galaxy collection install --upgrade community.general &&\
  ansible-galaxy collection install --upgrade community.windows
# COPY --from=certs /usr/local/share/ca-certificates /usr/local/share/ca-certificates
# COPY krb5.conf /etc/krb5.conf
RUN update-ca-certificates
RUN chown -R semaphore:0 /usr/local/bin/server-wrapper &&\
    chown -R semaphore:0 /usr/local/bin/semaphore
COPY config.json /etc/semaphore/config.json
WORKDIR /home/<USER>
#USER 1001

ENTRYPOINT ["/sbin/tini", "--"]
CMD ["/usr/local/bin/server-wrapper", "/usr/local/bin/semaphore", "server", "--config", "/etc/semaphore/config.json"]
