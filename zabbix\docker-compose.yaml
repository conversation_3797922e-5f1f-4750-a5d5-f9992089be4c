services:
  traefik:
    image: traefik:${TRAEFIK_TAG}
    restart: unless-stopped
    container_name: traefik
    ports:
      - "80:80"
      - "443:443"
    networks:
      - zbx_net_backend
      - zbx_net_frontend
      - back
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik/etc/traefik.yml:/traefik.yml
      - traefik_logs:/var/log/traefik
      - traefik_acme:/etc/traefik/acme
    labels:
      traefik.enable: "true"
      traefik.http.middlewares.traefik-auth.basicauth.users: "admin:$2a$12$8IxeBWgCCzNvjGWvtx02muYwZ1WnQB3zf6qw/8PgUPYeMrKRIhXSm"
      traefik.http.routers.traefik-secure.entrypoints: https
      traefik.http.routers.traefik-secure.middlewares: traefik-auth
      traefik.http.routers.traefik-secure.rule: Host(`$TRAEFIK_HOSTNAME`)
      traefik.http.routers.traefik-secure.tls.domains[0].main: ${TRAEFIK_HOSTNAME}
      #zmienuilem 
      traefik.http.routers.traefik-secure.tls: "true"
      traefik.http.routers.traefik-secure.tls.certresolver: letsencrypt
      traefik.http.routers.traefik-secure.service: api@internal
      traefik.http.services.traefik.loadbalancer.server.port: 8080
      traefik.http.routers.metrics.rule: PathPrefix(`/metrics`)
      traefik.http.routers.metrics.service: prometheus@internal
      traefik.http.routers.metrics.entrypoints: metrics
      traefik.http.routers.metrics.middlewares: metrics-auth
      traefik.http.middlewares.metrics-auth.basicauth.users: "admin:$2a$12$8IxeBWgCCzNvjGWvtx02muYwZ1WnQB3zf6qw/8PgUPYeMrKRIhXSm"
    env_file: ./traefik/etc/ovh.env
    healthcheck:
      test: ["CMD", "traefik", "healthcheck"]
      interval: 10s
      timeout: 5s
      retries: 3
  zabbix-server:
    build:
      context: .
      dockerfile: Dockerfile
    image: zabbix/zabbix-server-pgsql:${ZABBIX_SERVER_TAG}
    restart: unless-stopped
    container_name: zabbix-server
    hostname: zabbix-server
    # Dodatkowe zabezpieczenia po problemie z QNAP
    mem_limit: 2g
    mem_reservation: 1g
    cpus: 2.0
    stop_grace_period: 30s
    expose:
      - "10050"
      - "10051"
      - "10052"
    ports:
      - "10051:10051"
    volumes:
     - /etc/localtime:/etc/localtime:ro
     - /etc/timezone:/etc/timezone:ro
     - ./zbx_env/usr/lib/zabbix/alertscripts:/usr/lib/zabbix/alertscripts:ro
     - ./zbx_env/usr/lib/zabbix/externalscripts:/usr/lib/zabbix/externalscripts:ro
     - ./zbx_env/var/lib/zabbix/modules:/var/lib/zabbix/modules:ro
     - ./zbx_env/var/lib/zabbix/enc:/var/lib/zabbix/enc:ro
     - ./zbx_env/var/lib/zabbix/ssh_keys:/var/lib/zabbix/ssh_keys:ro
     - ./zbx_env/var/lib/zabbix/mibs:/var/lib/zabbix/mibs:ro
     - ./zbx_env/var/lib/zabbix/snmptraps:/var/lib/zabbix/snmptraps:ro
     - ./opsgenie/home/<USER>/home/<USER>
     - opsgenie_log:/var/log/opsgenie
     - ./zabbix-server/odbc.ini:/etc/odbc.ini
    links:
     - postgres-server:postgres-server
    ulimits:
     nproc: 32768
     nofile:
      soft: 10000
      hard: 20000
     memlock:
      soft: -1
      hard: -1
    env_file:
     - ./postgres/zabbix-server_db_env
     - ./zabbix-server/zabbix-server_env
     - ./zabbix-server/zabbix-server-web_env
    user: root
    depends_on:
     - postgres-server
    healthcheck:
      test: ["CMD", "zabbix_server", "-R", "config_cache_reload"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 120s
    networks:
      - zbx_net_backend
      - zbx_net_frontend
    sysctls:
     - net.ipv4.ip_local_port_range=1024 65000
     - net.ipv4.conf.all.accept_redirects=0
     - net.ipv4.conf.all.secure_redirects=0
     - net.ipv4.conf.all.send_redirects=0
    labels:
      com.zabbix.description: "Zabbix server with PostgreSQL database support"
      com.zabbix.company: "Zabbix LLC"
      com.zabbix.component: "zabbix-server"
      com.zabbix.dbtype: "pgsql"
      com.zabbix.os: "alpine"
  zabbix-web-nginx-pgsql:
   image: zabbix/zabbix-web-nginx-pgsql:${ZABBIX_FRONTEND_TAG}
   restart: unless-stopped
   container_name: zabbix-web
   expose:
    - "8080"
   links:
    - postgres-server:postgres-server
    - zabbix-server:zabbix-server
   volumes:
    - /etc/localtime:/etc/localtime:ro
    - /etc/timezone:/etc/timezone:ro
    - ./zabbix-server/sso/idp.crt:/usr/share/zabbix/conf/certs/idp.crt
    - ./zabbix-server/sso/idp.crt:/etc/zabbix/web/certs/idp.crt
   env_file:
    - ./postgres/zabbix-server_db_env
    - ./zabbix-server/zabbix-server_env
    - ./zabbix-server/zabbix-server-web_env
   user: root
   depends_on:
    - postgres-server
    - zabbix-server
   healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:8080"]
    interval: 30s
    timeout: 15s
    retries: 3
    start_period: 30s
   networks:
    zbx_net_backend:
     aliases:
      - zabbix-web-nginx-pgsql
      - zabbix-web-nginx-alpine-pgsql
      - zabbix-web-nginx-pgsql-alpine
   sysctls:
    - net.core.somaxconn=65535
   labels:
     com.zabbix.description: "Zabbix frontend on Nginx web-server with PostgreSQL database support"
     com.zabbix.company: "Zabbix LLC"
     com.zabbix.component: "zabbix-frontend"
     com.zabbix.webserver: "nginx"
     com.zabbix.dbtype: "pgsql"
     com.zabbix.os: "alpine"
     traefik.enable: "true"
     traefik.http.routers.zabbix-secure.entrypoints: https
     traefik.http.routers.zabbix-secure.rule: Host(`${ZABBIX_HOSTNAME}`)
     traefik.http.routers.zabbix-secure.middlewares: hsts-header@file
     traefik.http.routers.zabbix-secure.tls: "true"
     traefik.http.routers.zabbix-secure.tls.certresolver: letsencrypt
     traefik.http.routers.zabbix-secure.tls.domains[0].main: ${ZABBIX_HOSTNAME}
     traefik.http.routers.zabbix-secure.service: zabbix
     traefik.http.services.zabbix.loadbalancer.server.port: 8080

  zabbix-agent:
    image: zabbix/zabbix-agent2:${ZABBIX_AGENT_TAG}
    restart: unless-stopped
    container_name: zabbix-agent
    expose:
     - "10050"
     - "10051"
    volumes:
     - /etc/localtime:/etc/localtime:ro
     - /etc/timezone:/etc/timezone:ro
     - /var/run/docker.sock:/var/run/docker.sock:ro
    links:
     - zabbix-server:zabbix-server
    env_file:
     - ./zabbix-agent/zabbix-agent_env
    user: root
    privileged: true
    pid: "host"
    networks:
     - zbx_net_backend
    labels:
     com.zabbix.description: "Zabbix agent"
     com.zabbix.company: "Zabbix LLC"
     com.zabbix.component: "zabbix-agentd"
     com.zabbix.os: "alpine"
     traefik.enable: "false"
  postgres-server:
    image: postgres:${POSTGRES_TAG}
    restart: unless-stopped
    pull_policy: always
    container_name: postgres-server
    stop_grace_period: 2m
    shm_size: 2g
    command: >
      -c max_connections=400
      -c shared_buffers=1GB
      -c effective_cache_size=4GB
      -c maintenance_work_mem=100MB
      -c checkpoint_completion_target=0.7
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=300
      -c work_mem=10485kB
      -c min_wal_size=1GB
      -c max_wal_size=4GB
      -c max_worker_processes=16
      -c max_parallel_workers_per_gather=4
      -c max_parallel_workers=16
      -c max_parallel_maintenance_workers=4
      -c autovacuum_work_mem=1GB
      -c checkpoint_timeout=10min
    volumes:
     - postgresql_db:/var/lib/postgresql/data:rw
     - ./backup:/backup
     - ./postgres/scripts:/scripts
    env_file:
     - ./postgres/zabbix-server_db_env
    user: root
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U zabbix"]
      interval: 15s
      timeout: 15s
      retries: 5
    expose:
      - 5432
    networks:
     - zbx_net_backend
    labels:
      traefik.enable: "false"

  filebeat:
    image: docker.elastic.co/beats/filebeat-oss:${FILEBEAT_TAG}
    container_name: filebeat
    user: root
    restart: unless-stopped
    command: ["--strict.perms=false"]
    environment:
      - AXIOM_API_TOKEN=${AXIOM_API_TOKEN}  
    volumes:
     - /var/run/docker.sock:/var/run/docker.sock:ro
     - traefik_logs:/var/log/traefik:ro
     - filebeat_registry:/usr/share/filebeat/data
     - ./filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
     - zabbix-maintenance_logs:/var/log/zabbix-maintenance:ro
    expose:
      - "5601"
    networks:
      - zbx_net_backend
    labels:
      traefik.enable: "false"
    env_file:
      - ./.env
      
  mysql:
    restart: unless-stopped
    expose:
      - 3306
    image: mysql:8.0
    hostname: mysql
    volumes:
      - semaphore-mysql:/var/lib/mysql
    networks:
      - back
    labels:
      traefik.enable: "false"
    env_file:
      - ./semaphore/mysql.env
  semaphore:
      restart: unless-stopped
      container_name: semaphore
      expose:
        - "3000"
      build:
        context: .
        dockerfile: Dockerfile_2
      labels:
        traefik.enable: "true"
        traefik.http.routers.semaphore-secure.entrypoints: https
        traefik.http.routers.semaphore-secure.rule: Host(`${SEMAPHORE_HOSTNAME}`)
        traefik.http.routers.semaphore-secure.middlewares: hsts-header@file
        traefik.http.routers.semaphore-secure.tls: "true"
        traefik.http.routers.semaphore-secure.tls.certresolver: letsencrypt
        traefik.http.routers.semaphore-secure.service: semaphore
        traefik.http.services.semaphore.loadbalancer.server.port: 3000
        traefik.http.routers.semaphore-secure.tls.domains[0].main: ${SEMAPHORE_HOSTNAME}
      depends_on:
        - mysql
      networks:
        - back
      env_file:
        - ./semaphore/semaphore.env
      volumes:
        - semaphore:/etc/semaphore
        - semaphore-git:/tmp/semaphore    
  ofelia:
    image: mcuadros/ofelia:${OFELIA_TAG}
    restart: unless-stopped
    container_name: ofelia
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./ofelia/config.ini:/etc/config.ini
    command:
      - 'daemon'
      - '--config=/etc/config.ini'
    networks:
      - zbx_net_backend
    labels:
      traefik.enable: "false"
networks:
  zbx_net_frontend:
    driver: bridge
    driver_opts:
      com.docker.network.enable_ipv6: "false"
    ipam:
      driver: default
      config:
      - subnet: ************/24
  zbx_net_backend:
    driver: bridge
    driver_opts:
      com.docker.network.enable_ipv6: "false"
    ipam:
      driver: default
      config:
      - subnet: ************/24
  back:  # Definicja brakującej sieci
    driver: bridge
    driver_opts:
      com.docker.network.enable_ipv6: "false"
    ipam:
      driver: default
      config:
      - subnet: ************/24
volumes:
  postgresql_db: {}
  opsgenie_log: {}
  zabbix-maintenance_logs: {}
  traefik_logs: {}
  traefik_acme: {}
  filebeat_registry: {}
  semaphore:
  semaphore-git:
  semaphore-mysql:
