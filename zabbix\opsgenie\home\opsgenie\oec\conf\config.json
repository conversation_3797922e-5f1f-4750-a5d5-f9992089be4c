{"apiKey": "734a3158-b1e3-4857-a48c-4586848368c3", "baseUrl": "https://api.eu.opsgenie.com", "logLevel": "DEBUG", "globalArgs": [], "globalFlags": {"command_url": "https://zabbix.szkuat.pl/zabbix/api_jsonrpc.php", "user": "opsgenie_user", "password": "rahd6Soh"}, "actionMappings": {"Acknowledge": {"filepath": "/home/<USER>/oec/scripts/actionExecutorForZabbix4.py", "sourceType": "local", "env": [], "stdout": "/home/<USER>/oec/output/output.txt"}}, "pollerConf": {"pollingWaitIntervalInMillis": 100, "visibilityTimeoutInSec": 30, "maxNumberOfMessages": 10}, "poolConf": {"maxNumberOfWorker": 12, "minNumberOfWorker": 4, "monitoringPeriodInMillis": 15000, "keepAliveTimeInMillis": 6000, "queueSize": 0}}