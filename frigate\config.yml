# Konfiguracja Frigate z MQTT ale bez kamer
mqtt:
  enabled: true
  host: mosquitto
  port: 1883
  user: frigate
  password: frigatepass

database:
  path: /media/frigate/frigate.db

detectors:
  cpu1:
    type: cpu
    num_threads: 2

record:
  enabled: false
  retain:
    days: 1
    mode: motion

snapshots:
  enabled: false
  retain:
    default: 1

cameras:
  placeholder:
    enabled: false
    ffmpeg:
      inputs:
        - path: rtsp://placeholder:placeholder@127.0.0.1:554/placeholder
          roles:
            - detect
    
    objects:
      track:
        - person
    
    detect:
      enabled: false
      width: 640
      height: 480
      fps: 1
    
    record:
      enabled: false
    
    snapshots:
      enabled: false

birdseye:
  enabled: false

ui:
  timezone: Europe/Warsaw
  time_format: 24hour
  date_style: medium

logger:
  default: info
  logs:
    frigate.event: info