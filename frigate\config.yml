# Konfiguracja Frigate NVR
# Dokumentacja: https://docs.frigate.video/configuration/

# Wyłączone MQTT - nie jest wymagane do podstawowej pracy
# mqtt:
#   enabled: false

# Konfiguracja kamer
cameras:
  # Przykładowa konfiguracja kamery - dostosuj do swoich kamer
  wejsciowe:
    ffmpeg:
      inputs:
        - path: rtsp://login:haslo@adres-ip-kamery:554/ścieżka
          roles:
            - detect  # wykrywanie obiektów
            - rtmp    # strumień RTMP
            - record  # nagrywanie
    detect:
      width: 1920
      height: 1080
      fps: 5
    rtmp:
      enabled: true  # włączony strumień RTMP
    record:
      enabled: true  # nagrywanie włączone
      retain_days: 7  # przechowuj nagrania przez 7 dni
    snapshots:
      enabled: true  # zrzuty ekranu
      timestamp: true  # pokazuj znacznik czasu
      bounding_box: true  # pokazuj ramkę wykrytego obiektu
      retain:
        default: 30  # przechowuj zrzuty przez 30 dni

# Ustawienia wykrywania obiektów
detectors:
  cpu1:
    type: cpu  # wykorzystanie CPU do wykrywania obiektów

# Widok z lotu ptaka
birdseye:
  enabled: true
  width: 1280
  height: 720
  quality: 8
  mode: continuous  # ciągły podgląd

# Ustawienia nagrywania
record:
  enabled: true
  retain_days: 7  # przechowuj nagrania przez 7 dni

# Ustawienia strumienia RTMP
rtmp:
  enabled: true

# Ustawienia zrzutów ekranu
snapshots:
  enabled: true
  timestamp: true
  bounding_box: true
  retain:
    default: 30  # przechowuj zrzuty przez 30 dni

# Baza danych dla zdarzeń i nagrań
database:
  path: /media/frigate/frigate.db

# Ustawienia logowania
logger:
  default: info

# Opcjonalne: Przyspieszanie sprzętowe
# Odkomentuj i dostosuj do swojego sprzętu
# ffmpeg:
#   hwaccel_args: 
#     - -hwaccel
#     - vaapi
#     - -hwaccel_device
#     - /dev/dri/renderD128
#     - -hwaccel_output_format
#     - yuv420p

# Port serwera WWW
web_port: 5000
