name: Deploy to Local Server

on:
  push:
    branches:
      - main
  workflow_dispatch:

env:
  # Twoje zmienne
  V_DEPLOY_HOST: zabbix.szkuat.pl
  V_FOLDER: zabbix 

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Install Tailscale
      run: |
        curl -fsSL https://tailscale.com/install.sh | sh
        sudo tailscale up --authkey=${{ secrets.TAILSCALE_AUTHKEY }} --hostname=github-actions-runner --accept-routes --advertise-tags=tag:ci
    
    - name: Debug Tailscale connection
      run: |
        echo "Checking Tailscale status..."
        sudo tailscale status
        echo "Tailscale IP address:"
        sudo tailscale ip
        echo "Tailscale network information:"
        ip addr show tailscale0
        echo "Testing connectivity to target server:"
        ping -c 4 ${{ secrets.SSH_HOST }} || echo "Ping failed but continuing..."
    
    - name: Check Tailscale logs
      run: |
        echo "Tailscale daemon logs:"
        sudo journalctl -u tailscaled -n 50 || echo "Could not retrieve logs but continuing..."
    
    - name: Wait for Tailscale
      run: sleep 20
    
    - name: Create ovh.env with secrets
      run: |
        mkdir -p traefik/etc
        echo "OVH_ENDPOINT=ovh-eu" > traefik/etc/ovh.env
        echo "OVH_APPLICATION_KEY=${{ secrets.YOUR_APP_KEY }}" >> traefik/etc/ovh.env
        echo "OVH_APPLICATION_SECRET=${{ secrets.YOUR_APP_SECRET }}" >> traefik/etc/ovh.env
        echo "OVH_CONSUMER_KEY=${{ secrets.YOUR_CONSUMER_KEY }}" >> traefik/etc/ovh.env
        echo "Created OVH configuration file"
        ls -la traefik/etc
    
    - name: Copy files to server
      uses: appleboy/scp-action@v0.1.1
      with:
        host: ${{ secrets.SSH_HOST }}
        username: ${{ secrets.SSH_USER }}
        key: ${{ secrets.SSH_KEY }}
        source: "./*"
        target: "/srv/${{ env.V_FOLDER }}/"
        timeout: "180s"
    
    - name: Execute deployment script
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.SSH_HOST }}
        username: ${{ secrets.SSH_USER }}
        key: ${{ secrets.SSH_KEY }}
        timeout: "180s"
        script: |
          mkdir -p /srv/${{ env.V_FOLDER }} || true
          cd /srv/${{ env.V_FOLDER }}
          echo "Current directory: $(pwd)"
          echo "Files in directory:"
          ls -la
          echo "Current user: $(whoami)"
          echo "Stopping existing containers..."
          docker compose down || true
          echo "Pulling latest images..."
          docker compose pull || true
          echo "Starting containers..."
          docker compose up -d --build
          echo "Container status:"
          docker compose ps
          echo "Cleaning up..."
          docker image prune --all -f || true
          docker volume prune -f || true
          echo "Deployment completed."