name: Deploy Frigate with Traefik

on:
  push:
    branches: [ main ]
  workflow_dispatch:

env:
  V_DEPLOY_HOST: frigate.szkuat.pl
  V_FOLDER: frigate
  DEPLOY_PATH: /srv/frigate

jobs:
  deploy:
    runs-on: self-hosted
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Deploy to local server
      run: |
        echo "Starting deployment to ${{ env.DEPLOY_PATH }}/"
        
        # Create target directory with proper permissions
        sudo mkdir -p ${{ env.DEPLOY_PATH }}
        
        # Clean up existing deployment (keep important data)
        echo "Cleaning up old deployment..."
        sudo rm -rf ${{ env.DEPLOY_PATH }}/.git* || true
        sudo rm -rf ${{ env.DEPLOY_PATH }}/.github || true
        
        # Copy application files
        echo "Copying application files..."
        
        # Copy everything first using sudo
        sudo cp -r . ${{ env.DEPLOY_PATH }}/
        
        # Remove unnecessary files after copying
        sudo rm -rf ${{ env.DEPLOY_PATH }}/.git ${{ env.DEPLOY_PATH }}/.github ${{ env.DEPLOY_PATH }}/*.md ${{ env.DEPLOY_PATH }}/*.log ${{ env.DEPLOY_PATH }}/.gitignore 2>/dev/null || true
        
        # Set proper ownership (change to runner user)
        sudo chown -R $USER:$USER ${{ env.DEPLOY_PATH }}
        
        # Change to deployment directory
        cd ${{ env.DEPLOY_PATH }}
        
        echo "Current directory: $(pwd)"
        echo "Files in directory:"
        ls -la
        echo "Current user: $(whoami)"
        
        # Stop existing containers
        echo "Stopping existing containers..."
        docker compose down || sudo docker compose down || true
        
        # Pull latest images
        echo "Pulling latest images..."
        docker compose pull || sudo docker compose pull
        
        # Start containers
        echo "Starting containers..."
        docker compose up -d || sudo docker compose up -d
        
        # Wait a moment for containers to start
        sleep 10
        
        # Check container status
        echo "Checking container status..."
        docker compose ps || sudo docker compose ps
        
        # Verify containers are healthy
        if docker compose ps | grep -q "Up" || sudo docker compose ps | grep -q "Up"; then
          echo "✅ Deployment completed successfully!"
          echo "Frigate is available at: https://${{ env.V_DEPLOY_HOST }}"
        else
          echo "❌ Some containers failed to start"
          docker compose logs --tail=50 || sudo docker compose logs --tail=50
          exit 1
        fi