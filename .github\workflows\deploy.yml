name: Deploy Frigate with <PERSON><PERSON><PERSON><PERSON>

on:
  push:
    branches: [ main ]  # <PERSON><PERSON><PERSON> na nazwę swojej ga<PERSON>, jeśli inna niż main
  workflow_dispatch:

env:
  V_DEPLOY_HOST: frigate.szkuat.pl
  V_FOLDER: frigate

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Install Tailscale
      run: |
        curl -fsSL https://tailscale.com/install.sh | sh
        sudo tailscale up --authkey=${{ secrets.TAILSCALE_AUTHKEY }} --hostname=github-actions-runner --accept-routes --advertise-tags=tag:ci

    - name: Debug Tailscale connection
      run: |
        echo "Checking Tailscale status..."
        sudo tailscale status
        echo "Tailscale IP address:"
        sudo tailscale ip
        echo "Testing connectivity to target server:"
        ping -c 4 ${{ secrets.SSH_HOST }} || echo "Ping failed but continuing..."

    - name: Copy files to server
      uses: appleboy/scp-action@v0.1.4
      with:
        host: ${{ secrets.SSH_HOST }}
        username: ${{ secrets.SSH_USER }}
        key: ${{ secrets.SSH_KEY }}
        source: "."
        target: "/srv/${{ env.V_FOLDER }}/"
        rm: true
        timeout: "180s"

    - name: Execute deployment script
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.SSH_HOST }}
        username: ${{ secrets.SSH_USER }}
        key: ${{ secrets.SSH_KEY }}
        timeout: "180s"
        script: |
          mkdir -p /srv/${{ env.V_FOLDER }} || true
          cd /srv/${{ env.V_FOLDER }}
          echo "Current directory: $(pwd)"
          echo "Files in directory:"
          ls -la
          echo "Current user: $(whoami)"
          echo "Stopping existing containers..."
          docker compose down || true
          echo "Pulling latest images..."
          docker compose pull || true
          echo "Starting containers..."
          docker compose up -d
          echo "Checking container status..."
          docker compose ps
          echo "Deployment completed successfully!"
          echo "Frigate is available at: https://frigate.szkuat.pl"
