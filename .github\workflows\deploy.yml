name: Deploy Frigate with <PERSON>raefik

on:
  push:
    branches: [ main ]
  workflow_dispatch:

env:
  V_DEPLOY_HOST: frigate.szkuat.pl
  V_FOLDER: frigate
  DEPLOY_PATH: /srv/frigate

jobs:
  deploy:
    runs-on: self-hosted
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Setup Docker permissions
      run: |
        echo "=== Setting up Docker permissions ==="

        # Add runner to docker group if not already
        sudo usermod -aG docker $USER || true

        # Restart Docker service to ensure proper permissions
        sudo systemctl restart docker || true

        # Wait for Docker to be ready
        sleep 5

        # Test Docker access
        if ! docker ps >/dev/null 2>&1; then
          echo "Docker access still requires sudo, will use sudo for Docker commands"
          export USE_SUDO="sudo"
        else
          echo "Docker access working without sudo"
          export USE_SUDO=""
        fi

    - name: Prepare deployment directory
      run: |
        echo "=== Preparing deployment directory ==="

        # Create target directory with proper permissions
        sudo mkdir -p ${{ env.DEPLOY_PATH }}
        sudo chown -R $USER:$USER ${{ env.DEPLOY_PATH }}

        # Clean up existing deployment (keep important data)
        echo "Cleaning up old deployment..."
        rm -rf ${{ env.DEPLOY_PATH }}/.git* || true
        rm -rf ${{ env.DEPLOY_PATH }}/.github || true

    - name: Deploy application files
      run: |
        echo "=== Deploying application files ==="

        # Copy application files (excluding unnecessary files)
        rsync -av --delete \
          --exclude='.git' \
          --exclude='.github' \
          --exclude='*.md' \
          --exclude='*.log' \
          --exclude='.gitignore' \
          ./ ${{ env.DEPLOY_PATH }}/

        cd ${{ env.DEPLOY_PATH }}

        echo "Current directory: $(pwd)"
        echo "Files in directory:"
        ls -la

    - name: Create environment configuration
      run: |
        echo "=== Creating environment configuration ==="
        cd ${{ env.DEPLOY_PATH }}

        # Create .env file with required variables
        cat > .env << EOF
        # Traefik Configuration
        TRAEFIK_TAG=v2.10
        TRAEFIK_HOSTNAME=${{ env.V_DEPLOY_HOST }}

        # Application Configuration
        TZ=Europe/Warsaw
        PUID=1000
        PGID=1000
        EOF

        echo "Environment file created:"
        cat .env

    - name: Stop existing containers
      run: |
        echo "=== Stopping existing containers ==="
        cd ${{ env.DEPLOY_PATH }}

        # Try without sudo first, then with sudo if needed
        if docker compose down --remove-orphans --volumes 2>/dev/null; then
          echo "Containers stopped successfully"
        elif sudo docker compose down --remove-orphans --volumes 2>/dev/null; then
          echo "Containers stopped successfully (with sudo)"
        else
          echo "No existing containers to stop"
        fi

        # Clean up unused resources
        docker system prune -f 2>/dev/null || sudo docker system prune -f 2>/dev/null || true

    - name: Start services
      run: |
        echo "=== Starting services ==="
        cd ${{ env.DEPLOY_PATH }}

        # Validate docker-compose file
        echo "Validating docker-compose configuration..."
        if docker compose config --quiet 2>/dev/null; then
          USE_SUDO=""
        elif sudo docker compose config --quiet 2>/dev/null; then
          USE_SUDO="sudo"
        else
          echo "❌ Docker compose configuration is invalid"
          exit 1
        fi

        # Pull latest images
        echo "Pulling latest images..."
        $USE_SUDO docker compose pull

        # Start services
        echo "Starting containers..."
        $USE_SUDO docker compose up -d --remove-orphans

        # Wait for services to be ready
        echo "Waiting for services to start..."
        sleep 30

    - name: Verify deployment
      run: |
        echo "=== Verifying deployment ==="
        cd ${{ env.DEPLOY_PATH }}

        # Determine if we need sudo
        if docker compose ps >/dev/null 2>&1; then
          USE_SUDO=""
        else
          USE_SUDO="sudo"
        fi

        # Check container status
        echo "Container status:"
        $USE_SUDO docker compose ps

        # Check if containers are running
        RUNNING_CONTAINERS=$($USE_SUDO docker compose ps --services --filter "status=running" | wc -l)
        TOTAL_CONTAINERS=$($USE_SUDO docker compose ps --services | wc -l)

        echo "Running containers: $RUNNING_CONTAINERS/$TOTAL_CONTAINERS"

        if [ "$RUNNING_CONTAINERS" -gt 0 ]; then
          echo "✅ Deployment completed successfully!"
          echo "Frigate is available at: https://${{ env.V_DEPLOY_HOST }}"
          echo "Traefik dashboard: http://${{ env.V_DEPLOY_HOST }}:8080"

          # Show recent logs for troubleshooting
          echo "Recent logs:"
          $USE_SUDO docker compose logs --tail=20
        else
          echo "❌ No containers are running"
          echo "Container logs:"
          $USE_SUDO docker compose logs --tail=50
          exit 1
        fi