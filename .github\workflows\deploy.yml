name: Deploy Frigate with <PERSON><PERSON><PERSON><PERSON>

on:
  push:
    branches: [ main ]  # <PERSON><PERSON><PERSON> na nazwę swojej ga<PERSON>, jeśli inna niż main
  workflow_dispatch:

env:
  DOCKER_COMPOSE_VERSION: v2.26.0
  TRAEFIK_VERSION: v2.10

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Docker Compose
      uses: docker/setup-buildx-action@v2

    - name: Login to GitHub Container Registry
      uses: docker/login-action@v2
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Pull latest images
      run: |
        docker-compose -f docker-compose.yml pull

    - name: Stop and remove existing containers
      run: |
        docker-compose -f docker-compose.yml down --remove-orphans

    - name: Start containers
      run: |
        docker-compose -f docker-compose.yml up -d --build --remove-orphans

    - name: Verify deployment
      run: |
        # Sprawdź czy kontenery działają
        docker ps
        # Sprawdź czy Traefik działa
        curl -I http://localhost:8080/api/rawdata || echo "Traefik API check failed, but continuing..."

    - name: Clean up
      if: always()
      run: |
        docker system prune -f
        docker volume prune -f

    - name: Notify success
      if: success()
      run: |
        echo "Deployment completed successfully!"
        echo "Frigate is available at: https://frigate.szkuat.pl"

    - name: Notify failure
      if: failure()
      run: |
        echo "Deployment failed! Check the logs above for details."
