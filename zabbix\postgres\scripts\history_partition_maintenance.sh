#!/usr/bin/env bash

set -eo pipefail


# The trend ans history scripts start at the same time and both check
# and tries to install coreutils. A sleep and a loop whould ensure they
# end up installing the packages.
# apline linux don't support -d for date so we need
# to verify if date -d works and if not install coreutils
until date -d "+1 day" &>/dev/null
do
  if [ ! "$(which coreutils)" ];then
	apk add --no-cache --quiet --wait 30 coreutils || true
	sleep $(( (RANDOM % 10 )  + 1 ))s
	fi
done

# tables to create partitions for
tables=( history history_log history_str history_text history_uint )

for i in {1..5}; do
	# start and enddate for the partition to create and the suffix
	# to identify the partition
	startdate="$(date -d "+${i} day"|date -f - '+%s')"
	enddate=$(date -d "$(date -d @${startdate}) +1 days" | date -f - '+%s')
	suffix=$(date -d "+${i} day" "+%Y%m%d")


	# create a partition for each table the from value is included
	# in the partition the to value is excluded in the partition
	for table in "${tables[@]}"
	do
		psql -U zabbix zabbix -c "CREATE TABLE IF NOT EXISTS ${table}_${suffix} PARTITION OF $table FOR VALUES FROM ($startdate) TO ($enddate);"
	done
done


max=$(date -d "-8 days" "+%Y%m%d")
for table in "${tables[@]}"
do
	currentTables=($(psql -U zabbix zabbix -c "SELECT c.relname FROM pg_catalog.pg_class c JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace WHERE n.nspname = 'public' AND c.relname LIKE '${table}_2%' AND c.relkind = 'r'" | grep "${table}" | grep -v "default" ))

	for current in "${currentTables[@]}"
	do
		d=$(echo $current | grep -Eo "[0-9]+")
		if [[ $d < $max ]]
		then
			# detach partition don't support `if exists` so
			# we have to check if the table exists before
			# we try to remove it to avoid errors
			psql -U zabbix zabbix -c "ALTER TABLE $table DETACH PARTITION ${current};"
			psql -U zabbix zabbix -c "DROP TABLE ${current};"
		fi
	done
done
