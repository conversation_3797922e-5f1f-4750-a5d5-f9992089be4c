#[job-local "uname"]
#schedule = @hourly
#command = "uname -a"

[job-exec "logrotate_traefik"]
schedule = @every 24h
command = sh -c 'find /var/log/traefik -name *.log -type f -exec rm -rfv {} + && kill -USR1 1'
container = traefik

[job-exec "core_remove_postgres"]
schedule = @every 1h
command = sh -c 'find /var/lib/postgresql/data -name "core*" -type f -exec rm -rfv {} +'
container = postgres-server

[job-exec "history_partition_maintenance"]
schedule = 0 27 6 * * *
command = /usr/bin/env bash /scripts/history_partition_maintenance.sh
container = postgres-server

[job-exec "trends_partition_maintenance"]
schedule = 0 7 13 * * *
command = /usr/bin/env bash /scripts/trends_partition_maintenance.sh
container = postgres-server

[job-exec "add_bash"]
schedule = @hourly
command = /usr/bin/env sh -c 'apk add --no-cache --quiet --wait 30 bash'
container = postgres-server

[job-exec "full_vacuum"]
schedule = 0 0 22 * * 5
command = psql -U zabbix zabbix -c 'VACUUM FULL ANALYZE;'
container = postgres-server

[job-exec "send_watchdog"]
schedule = 0 10 * * *
command = /usr/lib/zabbix/alertscripts/msteamsh.sh 'Watchdog Watchdog'
container = zabbix-server

[job-exec "remove_history_text"]
schedule = @every 100m
command = psql -U zabbix zabbix -c 'DELETE FROM history_text_default h WHERE ctid IN ( SELECT h.ctid FROM history_text_default h LEFT JOIN items i ON i.itemid = h.itemid WHERE to_timestamp(h.clock) < (current_date - ((i.history)::interval)) LIMIT 100000);'
container = postgres-server

[job-exec "remove_history_str"]
schedule = @every 90m
command = psql -U zabbix zabbix -c 'DELETE FROM history_str_default h WHERE ctid IN ( SELECT h.ctid FROM history_str_default h LEFT JOIN items i ON i.itemid = h.itemid WHERE to_timestamp(h.clock) < (current_date - ((i.history)::interval)) LIMIT 10000);'
container = postgres-server

[job-exec "remove_trends"]
schedule = @every 50m
command = psql -U zabbix zabbix -c 'DELETE FROM trends_default t WHERE ctid IN ( SELECT t.ctid FROM trends_default t LEFT JOIN items i ON i.itemid = t.itemid WHERE to_timestamp(t.clock) < (current_date - ((i.trends)::interval)) LIMIT 10000);'
container = postgres-server

[job-exec "remove_trends_uint"]
schedule = @every 45m
command = psql -U zabbix zabbix -c 'DELETE FROM trends_uint_default t WHERE ctid IN ( SELECT t.ctid FROM trends_uint_default t LEFT JOIN items i ON i.itemid = t.itemid WHERE to_timestamp(t.clock) < (current_date - ((i.trends)::interval)) LIMIT 10000);'
container = postgres-server

[job-exec "remove_history"]
schedule = @every 21m
command = psql -U zabbix zabbix -c 'DELETE FROM history_default h WHERE ctid IN ( SELECT h.ctid FROM history_default h LEFT JOIN items i ON i.itemid = h.itemid WHERE to_timestamp(h.clock) < (current_date - ((i.history)::interval)) LIMIT 300000);'
container = postgres-server

[job-exec "remove_history_uint"]
schedule = @every 14m
command = psql -U zabbix zabbix -c 'DELETE FROM history_uint_default h WHERE ctid IN ( SELECT h.ctid FROM history_uint_default h LEFT JOIN items i ON i.itemid = h.itemid WHERE to_timestamp(h.clock) < (current_date - ((i.history)::interval)) LIMIT 500000);'
container = postgres-server
