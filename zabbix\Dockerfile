FROM zabbix/zabbix-server-pgsql:7.2.9-alpine as build
USER root
# RUN apk add --no-cache --update -t deps ca-certificates sudo nmap iputils openssl python3 python3-dev py3-pip tzdata curl grep bash pcre pcre-dev libevent libevent-dev libssh2 fping libcurl libxml2 net-snmp \
# && pip3 install --no-cache-dir --upgrade pip jmespath requests \
# && rm -rf /var/cache/apk/* \
# &&  if [[ ! -e /usr/bin/python ]];        then ln -sf /usr/bin/python3.8 /usr/bin/python; fi
 RUN wget -O mssql-tools18_18.2.1.1-1_amd64.apk https://download.microsoft.com/download/1/f/f/1fffb537-26ab-4947-a46a-7a45c27f6f77/mssql-tools18_18.2.1.1-1_amd64.apk
 RUN wget -O msodbcsql18_18.2.1.1-1_amd64.apk https://download.microsoft.com/download/1/f/f/1fffb537-26ab-4947-a46a-7a45c27f6f77/msodbcsql18_18.2.1.1-1_amd64.apk
RUN apk add --allow-untrusted msodbcsql18_18.2.1.1-1_amd64.apk
RUN apk add --allow-untrusted mssql-tools18_18.2.1.1-1_amd64.apk
