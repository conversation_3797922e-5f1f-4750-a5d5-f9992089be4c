# AWARYJNA konfiguracja Zabbix Server - MINIMUM procesów po problemie z QNAP
# <PERSON><PERSON><PERSON> konserwatywne wartości dla uniknięcia crashów SIGSEGV
#
# PROBLEM: Agent <PERSON><PERSON><PERSON><PERSON> na QNAP powoduje crashe SIGSEGV serwera
# ROZWIĄZANIE:
# 1. Ta konfiguracja używa minimum procesów
# 2. Po uruchomieniu serwera WYŁĄCZ QNAP w interfejsie web:
#    - Id<PERSON> do Configuration -> Hosts
#    - <PERSON><PERSON><PERSON>dź host QNAP
#    - Ustaw Status na "Disabled"
# 3. Stopniowo zwiększaj parametry gdy system będzie stabilny

# ZBX_LISTENIP=
# ZBX_HISTORYSTORAGEURL=http://elasticsearch:9200/
# ZBX_HISTORYSTORAGETYPES=uint,dbl,str,log,text
ZBX_DEBUGLEVEL=2                    # Zmniejszone do minimum
ZBX_STARTPOLLERS=3                  # MINIMUM - było 15, teraz 3
# ZBX_IPMIPOLLERS=0
ZBX_STARTPREPROCESSORS=2            # MINIMUM - było 5, teraz 2
ZBX_STARTLLDPROCESSORS=2            # MINIMUM - było 5, teraz 2
ZBX_STARTPOLLERSUNREACHABLE=1       # MINIMUM - było 5, teraz 1
ZBX_STARTTRAPPERS=3                 # MINIMUM - było 15, teraz 3
ZBX_STARTPINGERS=2                  # MINIMUM - było 10, teraz 2
ZBX_STARTDISCOVERERS=1              # MINIMUM - było 5, teraz 1
ZBX_STARTHTTPPOLLERS=2              # MINIMUM - było 10, teraz 2
ZBX_STARTTIMERS=1                   # MINIMUM - było 2, teraz 1
# ZBX_STARTESCALATORS=1
# ZBX_STARTALERTERS=3
ZBX_JAVAGATEWAY_ENABLE=false
# ZBX_JAVAGATEWAY=zabbix-java-gateway
# ZBX_JAVAGATEWAYPORT=10052
# ZBX_STARTJAVAPOLLERS=5
# ZBX_STARTVMWARECOLLECTORS=0
# ZBX_VMWAREFREQUENCY=60
# ZBX_VMWAREPERFFREQUENCY=60
# ZBX_VMWARECACHESIZE=8M
# ZBX_VMWARETIMEOUT=10
ZBX_ENABLE_SNMP_TRAPS=false
# ZBX_SOURCEIP=
ZBX_HOUSEKEEPINGFREQUENCY=1
ZBX_MAXHOUSEKEEPERDELETE=2000
# ZBX_SENDERFREQUENCY=30

# ZMNIEJSZONE Cache'e - oszczędność pamięci po problemie z QNAP
ZBX_CACHESIZE=256M                  # DRASTYCZNIE zmniejszone z 1G na 256M
# ZBX_CACHEUPDATEFREQUENCY=60
ZBX_STARTDBSYNCERS=2                # MINIMUM - było 4, teraz 2
ZBX_HISTORYCACHESIZE=256M           # DRASTYCZNIE zmniejszone z 1G na 256M
ZBX_HISTORYINDEXCACHESIZE=128M      # DRASTYCZNIE zmniejszone z 512M na 128M
ZBX_TRENDCACHESIZE=128M             # DRASTYCZNIE zmniejszone z 512M na 128M
ZBX_VALUECACHESIZE=128M             # DRASTYCZNIE zmniejszone z 512M na 128M

# BARDZO KRÓTKIE timeouty - ochrona przed problematycznym QNAP
ZBX_TIMEOUT=5                       # BARDZO KRÓTKIE - było 10, teraz 5 sekund
# ZBX_TRAPPERIMEOUT=300
ZBX_UNREACHABLEPERIOD=30            # BARDZO KRÓTKIE - było 60, teraz 30 sekund
ZBX_UNAVAILABLEDELAY=15             # BARDZO KRÓTKIE - było 30, teraz 15 sekund
ZBX_UNREACHABLEDELAY=15             # BARDZO KRÓTKIE - było 30, teraz 15 sekund

# ZBX_LOGSLOWQUERIES=3000
# ZBX_STARTPROXYPOLLERS=5
# ZBX_PROXYCONFIGFREQUENCY=3600
# ZBX_PROXYDATAFREQUENCY=1
# ZBX_LOADMODULE="dummy1.so,dummy2.so,dummy10.so"
# ZBX_TLSCAFILE=
# ZBX_TLSCRLFILE=
# ZBX_TLSCERTFILE=
# ZBX_TLSKEYFILE=
ZBX_SSO_SETTINGS={"strict":false, "baseurl":"https://zabbix.szkuat.pl/", "use_proxy_headers":true}