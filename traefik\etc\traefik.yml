global:
  checkNewVersion: false
log:
  level: DEBUG
  filePath: "/var/log/traefik/debug.log"
  format: common
accessLog:
  filePath: "/var/log/traefik/access.log"
  format: json
defaultEntryPoints:
   - http
   - https
api:
  dashboard: true
ping: {}
providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
  file:
    filename: ./traefik.yml
    watch: true
entryPoints:
  http:
    address: ":80"
    http:
      redirections:
        entryPoint:
          to: https
          scheme: https
  https:
    address: ":443"
  metrics:
    address: ":8082"
certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL>
      storage: /etc/traefik/acme/acme.json
      dnsChallenge:
        # used during the challenge
        provider: "ovh"
        delaybeforecheck: 60
        resolvers:
         - "*******:53"
         - "*******:53"
tls:
  options:
    default:
      minVersion: VersionTLS12
      cipherSuites:
        - TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384
        - TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
        - TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256
        - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
        - TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305
        - TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305
        # zmienilem
      sniStrict: false
# hsts
http:
  middlewares:
    hsts-header:
      headers:
        customResponseHeaders:
          Strict-Transport-Security: max-age=63072000
        stsseconds: 63072000
        stspreload: true
        stsincludesubdomains: false
metrics:
  prometheus:
    entryPoint: metrics
