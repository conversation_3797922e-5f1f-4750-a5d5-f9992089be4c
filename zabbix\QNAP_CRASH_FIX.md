# 🚨 NAPRAWA CRASHÓW ZABBIX SERVER - PROBLEM Z QNAP

## Problem
Zabbix Server ciągle się restartuje z błędami SIGSEGV po uruchomieniu agenta na QNAP.

## Rozwiązanie zastosowane w tej konfiguracji

### 1. Drastycznie zmniejszone procesy w `zabbix-server/zabbix-server_env`:
- **Pollers**: 15 → 3 (minimum)
- **Trappers**: 15 → 3 
- **Pingers**: 10 → 2
- **Preprocessors**: 5 → 2
- **DB Syncers**: 4 → 2
- **Timeouty**: 10s → 5s (bardzo krótkie)

### 2. Zmniejszone cache (oszczędność pamięci):
- **Cache Size**: 1G → 256M
- **History Cache**: 1G → 256M  
- **Index Cache**: 512M → 128M
- **Trend Cache**: 512M → 128M
- **Value Cache**: 512M → 128M

### 3. <PERSON><PERSON><PERSON> limity w `docker-compose.yaml`:
- **<PERSON><PERSON><PERSON><PERSON>**: limit 2GB, rezerwacja 1GB
- **CPU**: maksymalnie 2 rdzenie
- **Health check**: co 60s z timeout 30s

## ⚠️ KRYTYCZNE KROKI PO URUCHOMIENIU

### 1. Natychmiast po uruchomieniu serwera:
```
1. Idź do https://zabbix.szkuat.pl
2. Zaloguj się do interfejsu web
3. Idź do: Configuration → Hosts
4. Znajdź host QNAP
5. Kliknij na nazwę hosta QNAP
6. Zmień Status z "Enabled" na "Disabled"
7. Kliknij "Update"
```

### 2. Monitorowanie stabilności:
```bash
# Sprawdzenie czy kontener działa
docker ps | grep zabbix-server

# Sprawdzenie logów (nie powinno być SIGSEGV)
docker logs zabbix-server --tail=20

# Ciągłe monitorowanie
docker logs -f zabbix-server
```

### 3. Jeśli serwer nadal crashuje:
1. **Sprawdź czy QNAP jest wyłączony** w interfejsie web
2. Rozważ przywrócenie backup bazy danych sprzed problemu
3. Sprawdź czy inne urządzenia nie powodują problemów

## 🔧 Stopniowe zwiększanie wydajności

**Gdy system będzie stabilny przez 24h**, możesz stopniowo zwiększać:

1. **Najpierw zwiększ timeouty**:
   - `ZBX_TIMEOUT=5` → `ZBX_TIMEOUT=10`

2. **Potem zwiększ procesy**:
   - `ZBX_STARTPOLLERS=3` → `ZBX_STARTPOLLERS=5`
   - `ZBX_STARTTRAPPERS=3` → `ZBX_STARTTRAPPERS=5`

3. **Na końcu zwiększ cache**:
   - `ZBX_CACHESIZE=256M` → `ZBX_CACHESIZE=512M`

## 📋 Checklist po deployment

- [ ] Serwer uruchomił się bez crashów
- [ ] QNAP host wyłączony w interfejsie web  
- [ ] Brak błędów SIGSEGV w logach
- [ ] Interfejs web działa poprawnie
- [ ] Inne hosty są monitorowane normalnie

## 🆘 W razie dalszych problemów

1. **Natychmiast wyłącz QNAP** w interfejsie web
2. Sprawdź czy inne nowe urządzenia nie powodują problemów
3. Rozważ przywrócenie backup bazy danych
4. Skontaktuj się z administratorem systemu

---
**Ostatnia aktualizacja**: $(date)  
**Powód**: Crashe SIGSEGV po uruchomieniu agenta Zabbix na QNAP
