version: '3.8'

services:
  traefik:
    image: traefik:${TRAEFIK_TAG}
    restart: unless-stopped
    container_name: traefik
    ports:
      - "80:80"
      - "443:443"
    networks:
      - zbx_net_backend
      - zbx_net_frontend
      - back
      - frigate_network  # <PERSON><PERSON>o dla Frigate
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik/etc/traefik.yml:/traefik.yml
      - traefik_logs:/var/log/traefik
      - traefik_acme:/etc/traefik/acme
    labels:
      traefik.enable: "true"
      traefik.http.middlewares.traefik-auth.basicauth.users: "admin:$2a$12$8IxeBWgCCzNvjGWvtx02muYwZ1WnQB3zf6qw/8PgUPYeMrKRIhXSm"
      traefik.http.routers.traefik-secure.entrypoints: https
      traefik.http.routers.traefik-secure.middlewares: traefik-auth
      traefik.http.routers.traefik-secure.rule: Host(`$TRAEFIK_HOSTNAME`)
      traefik.http.routers.traefik-secure.tls.domains[0].main: ${TRAEFIK_HOSTNAME}
      traefik.http.routers.traefik-secure.tls: "true"
      traefik.http.routers.traefik-secure.tls.certresolver: letsencrypt
      traefik.http.routers.traefik-secure.service: api@internal
      traefik.http.services.traefik.loadbalancer.server.port: 8080
      traefik.http.routers.metrics.rule: PathPrefix(`/metrics`)
      traefik.http.routers.metrics.service: prometheus@internal
      traefik.http.routers.metrics.entrypoints: metrics
      traefik.http.routers.metrics.middlewares: metrics-auth
      traefik.http.middlewares.metrics-auth.basicauth.users: "admin:$2a$12$8IxeBWgCCzNvjGWvtx02muYwZ1WnQB3zf6qw/8PgUPYeMrKRIhXSm"
    env_file: ./traefik/etc/ovh.env
    healthcheck:
      test: ["CMD", "traefik", "healthcheck"]
      interval: 10s
      timeout: 5s
      retries: 3

  mosquitto:
    image: eclipse-mosquitto:2.0
    container_name: mosquitto
    restart: unless-stopped
    ports:
      - "1883:1883"
      - "9001:9001"
    volumes:
      - ./mosquitto/config/mosquitto.conf:/mosquitto/config/mosquitto.conf
      - ./mosquitto/config/passwd:/mosquitto/config/passwd
      - ./mosquitto/config/acl.conf:/mosquitto/config/acl.conf
      - ./mosquitto/data:/mosquitto/data
      - ./mosquitto/log:/mosquitto/log
    networks:
      - frigate_network

  frigate:
    container_name: frigate
    image: ghcr.io/blakeblackshear/frigate:stable
    shm_size: '256mb'
    restart: unless-stopped
    privileged: true
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Warsaw
    volumes:
      - ./frigate/config.yml:/config/config.yml
      - /etc/localtime:/etc/localtime:ro
      - frigate_recordings:/media/frigate
      - type: tmpfs
        target: /tmp/cache
        tmpfs:
          size: 1000000000
    ports:
      - "5000:5000"
      - "8554:8554"
      - "8555:8555"
      - "1984:1984"
    cap_add:
      - SYS_PTRACE
      - SYS_NICE
    depends_on:
      - mosquitto
    networks:
      - frigate_network
      - back  # Dodano żeby komunikować się z Traefik
    labels:
      traefik.enable: "true"
      traefik.http.routers.frigate-secure.entrypoints: https
      traefik.http.routers.frigate-secure.rule: Host(`frigate.szkuat.pl`)
      traefik.http.routers.frigate-secure.middlewares: hsts-header@file
      traefik.http.routers.frigate-secure.tls: "true"
      traefik.http.routers.frigate-secure.tls.certresolver: letsencrypt
      traefik.http.routers.frigate-secure.tls.domains[0].main: frigate.szkuat.pl
      traefik.http.routers.frigate-secure.service: frigate
      traefik.http.services.frigate.loadbalancer.server.port: 5000
      traefik.docker.network: back

volumes:
  frigate_recordings:
  traefik_logs:  # Dodano brakujące volume
  traefik_acme:  # Dodano brakujące volume

networks:
  frigate_network:
    driver: bridge
  zbx_net_backend:  # Dodano brakujące sieci
    driver: bridge
  zbx_net_frontend:
    driver: bridge
  back:
    driver: bridge