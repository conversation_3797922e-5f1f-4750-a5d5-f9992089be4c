version: '3.8'

services:
  frigate:
    container_name: frigate
    image: ghcr.io/blakeblackshear/frigate:stable
    shm_size: '256mb'
    restart: unless-stopped
    privileged: true
    network_mode: host  # Wymagane do przyspieszania sprzętowego i strumieniowania RTSP
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Warsaw
    volumes:
      - ./frigate/config.yml:/config/config.yml
      - /etc/localtime:/etc/localtime:ro
      - /sciezka/do/nagran:/media/frigate  # Zmień na właściwą ścieżkę do nagrań
      - type: tmpfs
        target: /tmp/cache
        tmpfs:
          size: 1000000000
    cap_add:
      - SYS_PTRACE
      - SYS_NICE
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frigate.rule=Host(`frigate.szkuat.pl`)"
      - "traefik.http.routers.frigate.entrypoints=websecure"
      - "traefik.http.routers.frigate.tls.certresolver=myresolver"
      - "traefik.http.services.frigate.loadbalancer.server.port=5000"
      - "traefik.http.routers.frigate.tls=true"
      - "traefik.http.routers.frigate.tls.certresolver=letsencrypt"
      - "traefik.http.routers.frigate.tls.domains[0].main=frigate.szkuat.pl"

  # Traefik jako reverse proxy
  traefik:
    image: traefik:v2.10
    container_name: traefik
    restart: unless-stopped
    command:
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.letsencrypt.acme.tlschallenge=true"
      - "--certificatesresolvers.letsencrypt.acme.email=<EMAIL>"  # Zmień na swój email
      - "--certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json"
      - "--api=true"
      - "--api.insecure=false"
      - "--api.dashboard=true"
      - "--api.debug=true"
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Port API Traefik
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik/letsencrypt:/letsencrypt
    healthcheck:
      test: ["CMD", "traefik", "healthcheck"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - default