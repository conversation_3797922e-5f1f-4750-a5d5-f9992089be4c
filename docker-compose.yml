services:
  frigate:
    container_name: frigate
    image: ${FRIGATE_IMAGE}
    shm_size: '256mb'
    restart: unless-stopped
    privileged: true
    environment:
      - PUID=${PUID}
      - PGID=${PGID}
      - TZ=${TZ}
    volumes:
      - ./frigate/config.yml:/config/config.yml
      - /etc/localtime:/etc/localtime:ro
      - frigate_recordings:/media/frigate
      - type: tmpfs
        target: /tmp/cache
        tmpfs:
          size: 1000000000
    ports:
      - "5000:5000"
      - "8554:8554"
      - "8555:8555"
      - "1984:1984"
    cap_add:
      - SYS_PTRACE
      - SYS_NICE
    networks:
      - zbx_net_backend
      - zbx_net_frontend
      - back
    labels:
      traefik.enable: "true"
      traefik.http.routers.frigate-secure.entrypoints: https
      traefik.http.routers.frigate-secure.rule: Host(`${FRIGATE_HOSTNAME}`)
      traefik.http.routers.frigate-secure.middlewares: hsts-header@file
      traefik.http.routers.frigate-secure.tls: "true"
      traefik.http.routers.frigate-secure.tls.certresolver: letsencrypt
      traefik.http.routers.frigate-secure.tls.domains[0].main: ${FRIGATE_HOSTNAME}
      traefik.http.routers.frigate-secure.service: frigate
      traefik.http.services.frigate.loadbalancer.server.port: 5000
      traefik.docker.network: back

  # Traefik jako reverse proxy
  traefik:
    image: traefik:${TRAEFIK_TAG}
    restart: unless-stopped
    container_name: traefik
    ports:
      - "80:80"
      - "443:443"
    networks:
      - zbx_net_backend
      - zbx_net_frontend
      - back
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik/etc/traefik.yml:/traefik.yml
      - traefik_logs:/var/log/traefik
      - traefik_acme:/etc/traefik/acme
    labels:
      traefik.enable: "true"
      traefik.http.middlewares.traefik-auth.basicauth.users: "admin:$2a$12$8IxeBWgCCzNvjGWvtx02muYwZ1WnQB3zf6qw/8PgUPYeMrKRIhXSm"
      traefik.http.routers.traefik-secure.entrypoints: https
      traefik.http.routers.traefik-secure.middlewares: traefik-auth
      traefik.http.routers.traefik-secure.rule: Host(`${TRAEFIK_HOSTNAME}`)
      traefik.http.routers.traefik-secure.tls.domains[0].main: ${TRAEFIK_HOSTNAME}
      traefik.http.routers.traefik-secure.tls: "true"
      traefik.http.routers.traefik-secure.tls.certresolver: letsencrypt
      traefik.http.routers.traefik-secure.service: api@internal
      traefik.http.services.traefik.loadbalancer.server.port: 8080
      traefik.http.routers.metrics.rule: PathPrefix(`/metrics`)
      traefik.http.routers.metrics.service: prometheus@internal
      traefik.http.routers.metrics.entrypoints: metrics
      traefik.http.routers.metrics.middlewares: metrics-auth
      traefik.http.middlewares.metrics-auth.basicauth.users: "admin:$2a$12$8IxeBWgCCzNvjGWvtx02muYwZ1WnQB3zf6qw/8PgUPYeMrKRIhXSm"
    env_file: ./traefik/etc/ovh.env
    healthcheck:
      test: ["CMD", "traefik", "healthcheck"]
      interval: 10s
      timeout: 5s
      retries: 3

volumes:
  frigate_recordings:
  traefik_logs:
  traefik_acme:

networks:
  zbx_net_frontend:
    driver: bridge
    driver_opts:
      com.docker.network.enable_ipv6: "false"
    ipam:
      driver: default
      config:
      - subnet: ************/24
  zbx_net_backend:
    driver: bridge
    driver_opts:
      com.docker.network.enable_ipv6: "false"
    ipam:
      driver: default
      config:
      - subnet: ************/24
  back:
    driver: bridge
    driver_opts:
      com.docker.network.enable_ipv6: "false"
    ipam:
      driver: default
      config:
      - subnet: ************/24