# traefik logs:
filebeat.inputs:
  - type: filestream
    id: traefik
    take_over: true
    enabled: true
    parsers:
      - ndjson:
          expand_keys: true
          add_error_key: true
          keys_under_root: true
    close.on_state_change.inactive: '1h'
    ignore_inactive: 'since_last_start'
    clean_inactive: '5h'
    paths:
      - /var/log/traefik/access.log
    fields_under_root: true
    fields:
      applicationName: traefik
      id: traefik
      traefik_version: '${TRAEFIK_TAG}'
      serviceName: '${SERVICE_NAME}'

processors:
  - add_cloud_metadata: ~
  - rename:
      fields:
        - from: "source"
          to: "file"
      ignore_missing: true
      fail_on_error: true

name: ${ZABBIX_HOSTNAME}
tags: ["traefik","zabbix"]

# Dodaj te ustawienia szablonu, które są wymagane gdy zmieniasz nazwę indeksu
setup.template.name: "koncertowa"
setup.template.pattern: "koncertowa.*"

# logstash
output.elasticsearch:
  hosts: ["https://api.axiom.co"]
  protocol: "https"
  username: "axiom"  # ni<PERSON><PERSON><PERSON><PERSON><PERSON>, ale wymagane
  password: "twój-token-bez-cudzysłowów"  # Wpisz tutaj swój token bez cudzysłowów
  index: "koncertowa.local"
  ssl.verification_mode: none

logging:
  level: info
  to_files: false
  to_stderr: true