# File created using '.gitignore Generator' for Visual Studio Code: https://bit.ly/vscode-gig

# Created by https://www.toptal.com/developers/gitignore/api/visualstudiocode,linux,backup
# Edit at https://www.toptal.com/developers/gitignore?templates=visualstudiocode,linux,backup

### Backup ###
*.bak
*.gho
*.ori
*.orig
*.tmp
*.dump

### Linux ###
*~

# temporary files which can be created if a process still has a handle open of a deleted file
.fuse_hidden*

# KDE directory preferences
.directory

# Linux trash folder which might appear on any partition or disk
.Trash-*

# .nfs files are created when an open file is removed but is still being accessed
.nfs*

### VisualStudioCode ###
.vscode/*
!.vscode/tasks.json
!.vscode/launch.json
*.code-workspace

### VisualStudioCode Patch ###
# Ignore all local history of files
.history
.ionide

# End of https://www.toptal.com/developers/gitignore/api/visualstudiocode,linux,backup

# Custom rules (everything added below won't be overriden by 'Generate .gitignore File' if you use 'Update' option)

