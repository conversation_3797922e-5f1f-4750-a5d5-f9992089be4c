#!/bin/bash

mkdir -p /oec-deb/usr/local/bin
s3URL=https://og-release-cicd-public-oregon.s3-us-west-2.amazonaws.com/purpose%3Dpublic/project%3Doec-builder/env%3Dprod/branch%3D${BRANCH}/module%3Doec-builder/version%3D${OEC_VERSION}/oec-linux-${OEC_VERSION}.zip
curl ${s3URL} --output ./oec.zip --silent --show-error --create-dirs
unzip ./oec.zip -d /oec-deb/usr/local/bin

cp -R $INPUT/. /oec-deb && \
mkdir -p /oec-deb/home/<USER>/oec/ && \
cp -R $OEC_SCRIPTS_REPO/$INTEGRATION/. /oec-deb/home/<USER>/oec && \
cp $OEC_SCRIPTS_REPO/release/oec-builder/oecScriptsVersion.json /oec-deb/home/<USER>/oec && \

INTEGRATION_VERSION=$(jq -r --arg v "$INTEGRATION" '.[$v]' /oec-deb/home/<USER>/oec/oecScriptsVersion.json)
rm /oec-deb/home/<USER>/oec/oecScriptsVersion.json && \

INTEGRATION_NAME=$(echo "$INTEGRATION" | awk '{print tolower($0)}')
INTEGRATION_PATH=opsgenie-${INTEGRATION_NAME}

####incoming part#######
INCOMING_PATH=/oec-deb/home/<USER>/oec/${INTEGRATION_PATH}
if [ -d "$INCOMING_PATH" ]; then
  go get -u github.com/alexcesaro/log && \
  cd ${INCOMING_PATH} && \
  GOOS=linux GOARCH=amd64 go build -o send2opsgenie send2opsgenie.go
fi
########################

sed -i "s|<path_of_script>|/home/<USER>/oec/scripts/actionExecutor.py|" /oec-deb/home/<USER>/oec/conf/config.json
sed -i "s|<path_of_output_file_of_script>|/home/<USER>/oec/output/output.txt|" /oec-deb/home/<USER>/oec/conf/config.json
sed -i "s/<local | git>/local/g" /oec-deb/home/<USER>/oec/conf/config.json

sed -i "s/%VERSION%/${INTEGRATION_VERSION}/g" /oec-deb/DEBIAN/control && \
sed -i "s/%INTEGRATION%/${INTEGRATION_PATH}/g" /oec-deb/DEBIAN/control && \

cd ~ && \
mkdir -p $OUTPUT/oec-packages-$OEC_VERSION/${INTEGRATION_PATH} && \
mkdir /${INTEGRATION_PATH} && \
mv /oec-deb/* /${INTEGRATION_PATH} && \

mkdir /deb-package && \
dpkg-deb -b /${INTEGRATION_PATH} /deb-package && \
cp -R /deb-package/${INTEGRATION_PATH}* $OUTPUT/oec-packages-$OEC_VERSION/${INTEGRATION_PATH}/
